import { Browser, chromium } from 'playwright';
import config from '../config';
class BrowserManager {
    private static instance: Browser | null = null;
    private static isInitializing = false;
    private static initPromise: Promise<Browser> | null = null;

    static async getInstance(): Promise<Browser> {
        if (this.instance && this.instance.isConnected()) {
            return this.instance;
        }

        if (this.isInitializing) {
            return this.initPromise!;
        }

        this.isInitializing = true;
        this.initPromise = this.initializeBrowser();

        try {
            this.instance = await this.initPromise;
            return this.instance;
        } finally {
            this.isInitializing = false;
            this.initPromise = null;
        }
    }

    private static async initializeBrowser(): Promise<Browser> {
        return chromium.launch({
            headless: config.browser.headless,
            args: [
                ...config.browser.args
            ],
            executablePath: config.browser.executablePath,
        });
    }

    static async closeBrowser() {
        if (this.instance) {
            await this.instance.close();
            this.instance = null;
        }
    }
}

export default BrowserManager;