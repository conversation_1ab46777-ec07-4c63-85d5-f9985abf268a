# Microsoft Account Reset API

这个API端点用于批量重置Microsoft账号的密码和绑定邮箱。

## 端点信息

- **URL**: `/reset`
- **方法**: `POST`
- **认证**: <PERSON><PERSON> (在请求头中设置 `Authorization: Bearer <your_token>`)

## 请求格式

发送一个包含账号信息的JSON数组：

```json
[
  {
    "id": 1,
    "email": "<EMAIL>",
    "password": "currentPassword123",
    "proofEmail": "<EMAIL>",
    "createDatetime": "",
    "resetStatus": 0,
    "resetDatetime": "",
    "resetFailMsg": "",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
  {
    "id": 2,
    "email": "<EMAIL>",
    "password": "currentPassword456",
    "proofEmail": "<EMAIL>",
    "createDatetime": "",
    "resetStatus": 0,
    "resetDatetime": "",
    "resetFailMsg": "",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  }
]
```

## 字段说明

- `id`: 账号唯一标识符
- `email`: Microsoft账号邮箱
- `password`: 当前密码
- `proofEmail`: 用于验证的邮箱（可选）
- `resetStatus`: 重置状态 (0=默认, 1=成功, 2=失败)
- `resetDatetime`: 重置时间
- `resetFailMsg`: 失败信息
- `initStatus`: 初始化状态 (0=默认, 1=成功, 2=失败)
- `initDatetime`: 初始化时间
- `initFailMsg`: 初始化失败信息

## 响应格式

成功响应：
```json
{
  "success": true,
  "message": "Account reset process completed",
  "results": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "password": "currentPassword123",
      "resetStatus": 1,
      "resetDatetime": "2024-01-01T12:00:00.000Z",
      "resetFailMsg": ""
    }
  ],
  "summary": {
    "total": 2,
    "success": 1,
    "failed": 1
  }
}
```

失败响应：
```json
{
  "success": false,
  "message": "Internal server error: Error details",
  "error": "Error details"
}
```

## 环境变量配置

在 `.env` 文件中设置以下变量：

```env
# API认证
API_TOKEN=your_secret_api_token_here
API_LOG_TOKEN=your_log_token_here

# 邮箱验证服务
PROOF_GODGODGAME_TOKEN=your_godgodgame_token
PROOF_IGIVEN_TOKEN=your_igiven_token

# 重置后的新密码
USER_NEW_PASSWORD=NewPassword123!

# 浏览器配置
BROWSER_TYPE=chromium
HEADLESS=false
```

## 使用示例

```bash
curl -X POST http://localhost:7860/reset \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_secret_api_token_here" \
  -d '[
    {
      "id": 1,
      "email": "<EMAIL>",
      "password": "oldPassword123",
      "resetStatus": 0,
      "resetDatetime": "",
      "resetFailMsg": ""
    }
  ]'
```

## 工作流程

1. 接收账号数组
2. 对每个账号执行以下操作：
   - 使用email和password登录Microsoft账号
   - 处理多重验证（如果需要）
   - 绑定新的验证邮箱
   - 修改密码为新密码
   - 更新账号状态
3. 将所有结果发送到远程日志服务器
4. 返回处理结果

## 注意事项

- 处理过程是串行的，不使用多线程
- 每个账号之间有随机延迟（2-5秒）
- 失败的账号会重试最多4次
- 所有操作都会记录日志
- 处理完成后会自动发送结果到远程服务器
