import { readBody } from 'h3';
import BrowserManager from '../utils/browser.ts';
import { getVerificationCode } from '../utils/emailHelper.ts';
import config from '../config.js';

 
/**
 * Microsoft账号重置任务类
 */
class MicrosoftAccountInitTask {
     
}

/**
 * 处理 `/health` 的 GET 请求，提供服务健康状态。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @returns {object} 包含服务状态和页面池信息的健康报告。
 */
export async function initHandler(event) {
    try {
        const accounts = await readBody(event);

        if (!Array.isArray(accounts) || accounts.length === 0) {
            throw new Error('Invalid request: accounts array is required');
        }

        const task = new MicrosoftAccountInitTask();
        const browser = await BrowserManager.getInstance();
        const context = await browser.newContext({
            ignoreHTTPSErrors: true
        });
        const page = await context.newPage();

        const results = [];

        try {
            for (let i = 0; i < accounts.length; i++) {
                const account = accounts[i];

                try {
                    const result = await task.handleSingleAccount(page, account.email, account.password, i + 1, accounts.length);

                    // 成功更新状态
                    account.resetStatus = 1;
                    account.resetFailMsg = "";

                    // 更新密码和验证邮箱
                    if (result && result.success) {
                        account.password = result.newPassword;
                        account.proofEmail = result.proofEmail;

                        if (result.alreadyReset) {
                            console.log(`账号 ${account.email} 之前已经重置过了`);
                        } else {
                            console.log(`账号 ${account.email} 重置成功`);
                        }
                    } else {
                        console.log(`账号 ${account.email} 重置成功`);
                    }
                } catch (error) {
                    // 失败更新状态
                    account.resetStatus = 2;
                    account.resetFailMsg = error.message;

                    console.error(`账号 ${account.email} 重置失败: ${error.message}`);
                }

                results.push(account);

                // 添加随机延迟
                if (i < accounts.length - 1) {
                    await page.waitForTimeout(Math.random() * 3000 + 2000);
                }
            }
        } finally {
            await page.close();
            await context.close();
        }

        // 发送结果到远程服务器
        try {
            const response = await fetch('https://seedlog.godgodgame.com/newaccount/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.app.logToken}`
                },
                body: JSON.stringify(results)
            });

            if (!response.ok) {
                console.error(`Failed to send results to remote server: ${response.statusText}`);
            } else {
                console.log('Results sent to remote server successfully');
            }
        } catch (error) {
            console.error(`Error sending results to remote server: ${error.message}`);
        }

        return {
            success: true,
            message: 'Account reset process completed',
            accounts: results,
            summary: {
                total: accounts.length,
                success: results.filter(r => r.resetStatus === 1).length,
                failed: results.filter(r => r.resetStatus === 2).length
            }
        };

    } catch (error) {
        console.error('Reset handler error:', error);
        return {
            success: false,
            message: `Internal server error: ${error.message}`,
            error: error.message
        };
    }
}